-- Jet Auto-Select & No Cooldown Script for Roblox
-- This script automatically selects Jet character and removes all ability cooldowns

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Services and remotes
local remotes = ReplicatedStorage:WaitForChild("Remotes")
local characterSelectRF = remotes:WaitForChild("OnCharacterSelectTouchedRF")

-- Character selection function
local function selectJetCharacter()
    print("🚀 Attempting to select Jet character...")
    
    -- Try to find Jet touch part in CharacterSelectTouchParts
    local workspace = game:GetService("Workspace")
    local spawn = workspace:FindFirstChild("Spawn")
    
    if spawn then
        local characterSelectParts = spawn:FindFirstChild("CharacterSelectTouchParts")
        if characterSelectParts then
            local jetPart = characterSelectParts:FindFirstChild("Jet")
            if jetPart then
                -- Use the remote function to select Jet
                local success, result = pcall(function()
                    return characterSelectRF:InvokeServer(jetPart)
                end)
                
                if success then
                    print("✅ Successfully selected Jet character!")
                    return true
                else
                    print("❌ Failed to select Jet via RemoteFunction:", result)
                end
            else
                print("❌ Jet touch part not found in CharacterSelectTouchParts")
            end
        else
            print("❌ CharacterSelectTouchParts folder not found")
        end
    else
        print("❌ Spawn folder not found in Workspace")
    end
    
    return false
end

-- Cooldown removal functions
local function removeCooldowns()
    print("🔥 Removing all ability cooldowns...")
    
    -- Remove tool cooldowns by hooking into the ToolCooldownUtil
    local function hookCooldownUtil()
        local playerScripts = player:FindFirstChild("PlayerScripts")
        if playerScripts then
            local clientMain = playerScripts:FindFirstChild("Client_Main")
            if clientMain then
                local modules = clientMain:FindFirstChild("Modules")
                if modules then
                    local cooldownUtil = modules:FindFirstChild("ToolCooldownUtil")
                    if cooldownUtil then
                        -- Hook the module to always return no cooldown
                        local originalRequire = require
                        local function newRequire(module)
                            if module == cooldownUtil then
                                return {
                                    StartCooldown = function() end,
                                    GetCooldown = function() return 0 end,
                                    IsCooldownActive = function() return false end
                                }
                            end
                            return originalRequire(module)
                        end
                        
                        -- Replace require function
                        getgenv().require = newRequire
                        print("✅ Hooked ToolCooldownUtil successfully!")
                    end
                end
            end
        end
    end
    
    -- Remove attack cooldowns from character assets
    local function removeAttackCooldowns()
        local characterAssets = ReplicatedStorage:FindFirstChild("CharacterAssets")
        if characterAssets then
            for _, character in pairs(characterAssets:GetChildren()) do
                for _, asset in pairs(character:GetChildren()) do
                    local configs = asset:FindFirstChild("Configs")
                    if configs then
                        local attackCooldown = configs:FindFirstChild("AttackCooldown")
                        if attackCooldown and attackCooldown:IsA("IntValue") then
                            attackCooldown.Value = 0
                        end
                    end
                end
            end
            print("✅ Removed attack cooldowns from character assets!")
        end
    end
    
    -- Hook jetpack abilities
    local function hookJetpackAbilities()
        local jetpackToggle = remotes:FindFirstChild("OnGizmoidJetpackToggle")
        local jetpackSetup = remotes:FindFirstChild("OnGizmoidJetpackSetup")
        
        if jetpackToggle then
            -- Create a connection to spam jetpack toggle
            local connection
            connection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
                if not gameProcessed and input.KeyCode == Enum.KeyCode.F then -- F key for jetpack
                    jetpackToggle:FireServer()
                end
            end)
            print("✅ Hooked jetpack toggle (Press F to use)!")
        end
    end
    
    -- Execute all cooldown removal functions
    hookCooldownUtil()
    removeAttackCooldowns()
    hookJetpackAbilities()
end

-- Main execution function
local function main()
    print("🎮 Jet Auto-Select & No Cooldown Script Started!")
    print("=" .. string.rep("=", 50))
    
    -- Wait for game to load
    if not player.Character then
        player.CharacterAdded:Wait()
    end
    
    wait(2) -- Give some time for everything to load
    
    -- Select Jet character
    local jetSelected = selectJetCharacter()
    
    -- Remove cooldowns regardless of character selection success
    removeCooldowns()
    
    -- Continuous cooldown removal loop
    local function continuousCooldownRemoval()
        while true do
            wait(1) -- Check every second
            
            -- Keep removing attack cooldowns
            local characterAssets = ReplicatedStorage:FindFirstChild("CharacterAssets")
            if characterAssets then
                for _, character in pairs(characterAssets:GetChildren()) do
                    for _, asset in pairs(character:GetChildren()) do
                        local configs = asset:FindFirstChild("Configs")
                        if configs then
                            local attackCooldown = configs:FindFirstChild("AttackCooldown")
                            if attackCooldown and attackCooldown:IsA("IntValue") and attackCooldown.Value > 0 then
                                attackCooldown.Value = 0
                            end
                        end
                    end
                end
            end
        end
    end
    
    -- Start continuous cooldown removal in a separate thread
    spawn(continuousCooldownRemoval)
    
    print("=" .. string.rep("=", 50))
    print("🎯 Script fully loaded!")
    print("📋 Features:")
    print("   • Auto-selected Jet character")
    print("   • Removed all ability cooldowns")
    print("   • Continuous cooldown prevention")
    print("   • Press F for jetpack abilities")
    print("=" .. string.rep("=", 50))
end

-- Advanced cooldown bypassing methods
local function advancedCooldownBypass()
    print("🔧 Setting up advanced cooldown bypass...")

    -- Hook into all RemoteEvents and RemoteFunctions to bypass server-side cooldowns
    local function hookRemotes()
        local originalFireServer = Instance.new("RemoteEvent").FireServer
        local originalInvokeServer = Instance.new("RemoteFunction").InvokeServer

        -- Hook RemoteEvent FireServer
        local function newFireServer(self, ...)
            local args = {...}
            -- Remove any cooldown-related arguments or modify them
            return originalFireServer(self, unpack(args))
        end

        -- Hook RemoteFunction InvokeServer
        local function newInvokeServer(self, ...)
            local args = {...}
            -- Remove any cooldown-related arguments or modify them
            return originalInvokeServer(self, unpack(args))
        end

        -- Apply hooks to all existing remotes
        for _, remote in pairs(remotes:GetChildren()) do
            if remote:IsA("RemoteEvent") then
                remote.FireServer = newFireServer
            elseif remote:IsA("RemoteFunction") then
                remote.InvokeServer = newInvokeServer
            end
        end

        print("✅ Hooked all remote events and functions!")
    end

    -- Memory patching for cooldown values
    local function patchCooldownMemory()
        -- Continuously set all cooldown-related values to 0
        spawn(function()
            while true do
                wait(0.1) -- Very frequent checks

                -- Patch player-specific cooldowns
                if player.Character then
                    for _, obj in pairs(player.Character:GetDescendants()) do
                        if obj.Name:lower():find("cooldown") and obj:IsA("NumberValue") then
                            obj.Value = 0
                        elseif obj.Name:lower():find("cooldown") and obj:IsA("IntValue") then
                            obj.Value = 0
                        end
                    end
                end

                -- Patch global cooldowns
                for _, obj in pairs(ReplicatedStorage:GetDescendants()) do
                    if obj.Name:lower():find("cooldown") and obj:IsA("NumberValue") then
                        obj.Value = 0
                    elseif obj.Name:lower():find("cooldown") and obj:IsA("IntValue") then
                        obj.Value = 0
                    end
                end
            end
        end)

        print("✅ Started memory patching for cooldowns!")
    end

    hookRemotes()
    patchCooldownMemory()
end

-- Enhanced ability spamming
local function setupAbilitySpamming()
    print("⚡ Setting up ability spamming...")

    -- Key bindings for rapid ability usage
    local abilityKeys = {
        [Enum.KeyCode.Q] = "Ability1",
        [Enum.KeyCode.E] = "Ability2",
        [Enum.KeyCode.R] = "Ability3",
        [Enum.KeyCode.T] = "Ability4",
        [Enum.KeyCode.F] = "Jetpack"
    }

    local isSpamming = {}

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end

        local keyCode = input.KeyCode
        if abilityKeys[keyCode] then
            isSpamming[keyCode] = true

            -- Start spamming the ability
            spawn(function()
                while isSpamming[keyCode] do
                    -- Fire all relevant remotes rapidly
                    for _, remote in pairs(remotes:GetChildren()) do
                        if remote:IsA("RemoteEvent") then
                            pcall(function()
                                remote:FireServer()
                            end)
                        end
                    end
                    wait(0.01) -- Very fast spamming
                end
            end)
        end
    end)

    UserInputService.InputEnded:Connect(function(input, gameProcessed)
        local keyCode = input.KeyCode
        if abilityKeys[keyCode] then
            isSpamming[keyCode] = false
        end
    end)

    print("✅ Ability spamming setup complete!")
    print("📋 Controls:")
    for key, ability in pairs(abilityKeys) do
        print("   • " .. key.Name .. " = " .. ability .. " (Hold to spam)")
    end
end

-- Execute the main function
main()

-- Execute advanced features
wait(1)
advancedCooldownBypass()
setupAbilitySpamming()

print("🚀 All systems operational! Jet character with unlimited abilities ready!")
